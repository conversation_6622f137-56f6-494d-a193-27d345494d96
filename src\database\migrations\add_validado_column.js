import sequelize from '../database.js';

async function addValidadoColumn() {
  try {
    console.log('Starting migration: Adding validado column to oa_procesos table...');
    
    // Add the validado column to oa_procesos table
    await sequelize.query(`
      ALTER TABLE oferta_academica.oa_procesos 
      ADD COLUMN IF NOT EXISTS validado BOOLEAN NOT NULL DEFAULT false;
    `);
    
    console.log('✅ Successfully added validado column to oa_procesos table');
    
    // Add comment to document the column purpose
    await sequelize.query(`
      COMMENT ON COLUMN oferta_academica.oa_procesos.validado IS 'Indicates whether the proceso has been validated (all subprocesos must be validated first)';
    `);
    
    console.log('✅ Added column comment');
    
    // Create an index for better query performance
    await sequelize.query(`
      CREATE INDEX IF NOT EXISTS idx_oa_procesos_validado ON oferta_academica.oa_procesos(validado);
    `);
    
    console.log('✅ Created index on validado column');
    
    // Verify the column was added successfully
    const result = await sequelize.query(`
      SELECT column_name, data_type, is_nullable, column_default 
      FROM information_schema.columns 
      WHERE table_schema = 'oferta_academica' 
        AND table_name = 'oa_procesos' 
        AND column_name = 'validado';
    `);
    
    if (result[0].length > 0) {
      console.log('✅ Migration completed successfully!');
      console.log('Column details:', result[0][0]);
    } else {
      console.log('❌ Column was not created');
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await sequelize.close();
  }
}

// Run the migration
addValidadoColumn();
