import express from "express";
import { checkToken } from "../auth/auth.js";
import { checkPermissions } from "../auth/checkPermission.js";
import { sequelize } from "../database/database.js";

// Notificacion
import {
  getNotificacion,
  countNotificacion,
  postNotificacion,
} from "../controllers/Notificaciones/notificacionController.js";

// Usuario
import { getAllUsuarios } from "../controllers/Usuario/usuarioController.js";
import { getUsuariosAreaEncargado } from "../controllers/Usuario/usuarioController.js";
import { postUsuario } from "../controllers/Usuario/usuarioController.js";

// InfraestructuraRecurso
import {
  getInfraestructuraRecurso,
  postInfraestructuraRecurso,
  confirmInfraestructuraRecurso,
  getAllInfraestructuraRecurso,
  deleteInfraestructuraRecurso,
} from "../controllers/Oti/infraestructuraRecursoController.js";

// inmueblePermanente
import {
  getInmueblePermanente,
  getAllInmueblePermanente,
  editInmueblePermanente,
  postInmueblePermanente,
  deleteInmueblePermanente,
} from "../controllers/Oti/inmueblePermanenteController.js";

// inmuebleRestringido
import {
  getInmuebleRestringido,
  getAllInmuebleRestringido,
  editInmuebleRestringido,
  postInmuebleRestringido,
  deleteInmuebleRestringido,
} from "../controllers/Oti/inmuebleRestringidoController.js";

// biblioteca
import {
  getBiblioteca,
  getAllBiblioteca,
  editBiblioteca,
  postBiblioteca,
  deleteBiblioteca,
} from "../controllers/Oti/bibliotecaController.js";

// librosBasesDigitales
import {
  getLibrosBasesDigitales,
  getAllLibrosBasesDigitales,
  editLibrosBasesDigitales,
  postLibrosBasesDigitales,
  deleteLibrosBasesDigitales,
} from "../controllers/Oti/librosBasesDigitalesController.js";

// predio
import {
  getPredio,
  getAllPredios,
  editPredio,
  postPredio,
  deletePredio,
} from "../controllers/Oti/predioController.js";

// plataformaVirtual
import {
  getPlataformaVirtual,
  getAllPlataformaVirtual,
  editPlataformaVirtual,
  postPlataformaVirtual,
  deletePlataformaVirtual,
} from "../controllers/Oti/plataformaVirtualController.js";

import { getAllRoles } from "../controllers/Mantenedores/rol_Controller.js";
import { getAllRecursos } from "../controllers/Mantenedores/recurso_Controller.js";
import {
  postAcceso,
  deleteAcceso,
} from "../controllers/Usuario/accesoController.js";
//oferta academica
// import {
//   getOAcademicaById,
//   getOacademicas,
//   getOacademicasHistorico,
//   updateOAcademica,
//   postOAcademica,
//   getCsvOacademicas,
//   updateOacademicaEtapa1Pregrado,
//   updateOacademicaEtapa1PosgradoPostitulo,
//   updateOacademicaEtapa3Pregrado,
//   //david meza
//   getOAcademicaByProcesoSiesId,
//   postOAcademicaFromCSV,
//   deleteOAcademicaByEtapaAndProceso
// } from "../controllers/Oferta-Academica/ofertaAcademicaControllerVictor.js";
// //Proceso SIES
import {
  postProcesoSies,
  getAllProcesoSies,
  deleteProcesoSies,
  getProcesoSiesOAbyYear,
  getProcesoSiesOAactual,
} from "../controllers/Mantenedores/proceso_sies_Controller.js";
import {
  getAllComunas,
  postComuna,
  deleteComuna,
} from "../controllers/Mantenedores/comunas_Controller.js";

import {
  getAllMU_pregrado,
  getMU_pregrado,
  exportCSV,
  postMU_pregrado,
  deleteMU_pregrado,
  confirm_MU_pregrado,
  getAllFechas_pregrado,
  getFechas_pregrado,
  postFechas_pregrado,
  postEstudiantesFromCSV,
  deleteEstudiantes,
} from "../controllers/Matricula-Unificada/Pregrado/MU_pregradoController.js";

import {
  getAllMU_postgrado,
  getMU_postgrado,
  postMU_postgrado,
  deleteMU_postgrado,
  confirm_MU_PP,
  exportCSVPP,
  deleteEstudiantesPP,
  postEstudiantesPpFromCSV,
  getAllFechas_postgrado,
  getFechas_postgrado,
  postFechas_postgrado,
} from "../controllers/Matricula-Unificada/Postgrado-Postitulo/MU_postgradoPostituloController.js";

// Proceso David
import {
  getAllOAProcesos,
  getOAProcesoById,
  createOAProceso,
  updateOAProceso,
  deleteOAProceso,
  validateOAProceso,
} from "../controllers/Oferta-Academica-David/oaProcesoController.js";

// SubProceso David
import {
  getAllOASubProcesos,
  getOASubProcesosByProcesoId,
  getOASubProcesoById,
  createOASubProceso,
  updateOASubProceso,
  deleteOASubProceso,
  createSubProceso1WithEtapas,
  createSubProceso2WithEtapas,
} from "../controllers/Oferta-Academica-David/oaSubProcesoController.js";

import {
  getAllOAEtapas,
  getOAEtapasBySubProcesoId,
  getOAEtapaById,
  createOAEtapa,
  updateOAEtapa,
  deleteOAEtapa,
  // createEtapaWithOfertasFromCSV,
  processCSVForOfertasAcademicas,
  validateOAEtapa,
} from "../controllers/Oferta-Academica-David/oaEtapaController.js";

// OAOfertaAcademica David
import {
  getAllOAOfertaAcademicas,
  getOAOfertaAcademicaById,
  getOAOfertaAcademicasByEtapaId,
  createOAOfertaAcademica,
  updateOAOfertaAcademica,
  deleteOAOfertaAcademica,
  deleteOAOfertaAcademicasByEtapaId,
  getCantCarrerasPerEtapa as getOACantCarrerasPerEtapa,
  importOfertasAcademicasFromCSV,
  importOfertasAcademicasFromCSVTotal,
  getOAOfertaAcademicasByEtapaIdForCSV,
  deleteOAOfertaAcademicasBySubprocesoEtapaTipo,
  getOAOfertaAcademicasTotalsByEtapaCSV,
} from "../controllers/Oferta-Academica-David/oaOfertaAcademicaController.js";

// import { savePdfAsBlob } from "../controllers/Test/pdfTestController.js";
const router = express.Router();

// //Oferta Académica routes
// router.get("/oacademicas/", getOacademicas); // get all "ofertas academicas"
// router.get("/oacademicashistorico/:etapaId", getOacademicasHistorico); // get all "ofertas academicas historico"
// router.get("/getcsvoacademicas/", getCsvOacademicas); // get csv "ofertas academicas"
// router.get("/oacademica/:oa_sies_id", getOAcademicaById); // get specific "oferta académica" by id
// router.patch("/oacademica/:oa_sies_id", updateOAcademica);
// router.post("/oacademica/", postOAcademica);
// router.patch(
//   "/updateoacademicaetapa1pregrado/",
//   updateOacademicaEtapa1Pregrado
// );
// router.patch(
//   "/updateoacademicaetapa3pregrado/",
//   updateOacademicaEtapa3Pregrado
// );
// router.patch(
//   "/updateoacademicaetapa1posgradopostitulo/",
//   updateOacademicaEtapa1PosgradoPostitulo
// );
// // Oferta Académica by Proceso SIES ID
// router.get("/oacademica/procesosies/:proceso_sies_id", getOAcademicaByProcesoSiesId);
// //Oferta Academica David Meza
// //postOAcademica es la antigua de victor
// // router.get("/ofertaAcademica/", getAllOfertaAcademica);
// // router.post("/ofertaAcademica/", createOfertaAcademica);
// router.post("/ofertaAcademica/cargarCSV/:proceso_sies_id/:etapa_actual", postOAcademicaFromCSV);
// router.delete("/ofertaAcademica/:proceso_sies_id/:etapa",deleteOAcademicaByEtapaAndProceso);
// // Cantidad de Carreras por Etapa routes

// notificacion routes
router.get("/notificacion/:usuario_email", getNotificacion);
router.post("/notificacion/", postNotificacion);
router.get("/notificacion/count/:usuario_email", countNotificacion);

// usuario routes
router.get("/usuario/", getAllUsuarios);
router.get("/usuarioAreasEncargado/:email", getUsuariosAreaEncargado);
router.post("/usuario/", postUsuario);
router.get(
  "/protected",
  checkToken,
  checkPermissions("administrador", "all", "all"),
  (req, res) => {
    res.send(`Hello ${req.user.email}, you have accessed a protected route!`);
  }
);
router.get("/validateLogin", checkToken, async (req, res) => {
  try {
    console.log("VALIDATION OF THE DATABASE CONNECTION IN 'VALIDATELOGIN'");
    await sequelize.authenticate();

    if (req.tokenValid) {
      return res
        .status(200)
        .json({ success: true, message: "valid token", user: req.user });
    } else {
      return res.status(401).json({ success: false, message: "Invalid token" });
    }
  } catch (error) {
    if (error.name.toLowerCase().includes("sequelize")) {
      console.error("Database connection error:");
      return res.status(500).json({
        error: "Database host not found. Please check the configuration.",
      });
    }
    // Generic error handling for other errors
    console.error("An unexpected error occurred:", error.message);
    res.status(500).json({ error: "An internal server error occurred." });
  }
});

// Acceso routes
router.delete("/acceso/", deleteAcceso);
router.post("/acceso/", postAcceso);

// infraestructuraRecursos routes
router.get("/infraestructuraRecurso/:anio_proceso", getInfraestructuraRecurso);
router.get("/infraestructuraRecurso/", getAllInfraestructuraRecurso);
router.post("/infraestructuraRecurso/", postInfraestructuraRecurso);
router.patch(
  "/infraestructuraRecurso/:anio_proceso",
  confirmInfraestructuraRecurso
);
router.delete(
  "/infraestructuraRecurso/:anio_proceso",
  deleteInfraestructuraRecurso
);

// inmueblePermanente routes
router.get("/inmueblePermanente/:inmueblePermanente_id", getInmueblePermanente);
router.get(
  "/inmueblePermanenteAll/:infraestructuraRecurso_id",
  getAllInmueblePermanente
);
router.patch(
  "/inmueblePermanente/:inmueblePermanente_id",
  editInmueblePermanente
);
router.post("/inmueblePermanente/", postInmueblePermanente);
router.delete(
  "/inmueblePermanente/:inmueblePermanente_id",
  deleteInmueblePermanente
);

// inmuebleRestringido routes
router.get(
  "/inmuebleRestringido/:inmuebleRestringido_id",
  getInmuebleRestringido
);
router.get(
  "/inmuebleRestringidoAll/:infraestructuraRecurso_id",
  getAllInmuebleRestringido
);
router.patch(
  "/inmuebleRestringido/:inmuebleRestringido_id",
  editInmuebleRestringido
);
router.post("/inmuebleRestringido/", postInmuebleRestringido);
router.delete(
  "/inmuebleRestringido/:inmuebleRestringido_id",
  deleteInmuebleRestringido
);

// Biblioteca routes
router.get("/biblioteca/:biblioteca_id", getBiblioteca);
router.get("/bibliotecaAll/:infraestructuraRecurso_id", getAllBiblioteca);
router.patch("/biblioteca/:biblioteca_id", editBiblioteca);
router.post("/biblioteca/", postBiblioteca);
router.delete("/biblioteca/:biblioteca_id", deleteBiblioteca);

// LibrosBasesDigitales routes
router.get("/librosBasesDigitales/:libro_id", getLibrosBasesDigitales);
router.get(
  "/librosBasesDigitalesAll/:infraestructuraRecurso_id",
  getAllLibrosBasesDigitales
);
router.patch("/librosBasesDigitales/:libro_id", editLibrosBasesDigitales);
router.post("/librosBasesDigitales/", postLibrosBasesDigitales);
router.delete("/librosBasesDigitales/:libro_id", deleteLibrosBasesDigitales);

// Predio routes
router.get("/predio/:predio_id", getPredio);
router.get("/predioAll/:infraestructuraRecurso_id", getAllPredios);
router.patch("/predio/:predio_id", editPredio);
router.post("/predio/", postPredio);
router.delete("/predio/:predio_id", deletePredio);

// PlataformaVirtual routes
router.get("/plataformaVirtual/:plataforma_id", getPlataformaVirtual);
router.get(
  "/plataformaVirtualAll/:infraestructuraRecurso_id",
  getAllPlataformaVirtual
);
router.patch("/plataformaVirtual/:plataforma_id", editPlataformaVirtual);
router.post("/plataformaVirtual/", postPlataformaVirtual);
router.delete("/plataformaVirtual/:plataforma_id", deletePlataformaVirtual);

// Roles routes
router.get("/roles/", getAllRoles);

// Recursos routes
router.get("/recursos/:email", getAllRecursos);

// Proceso SIES routes
router.get("/procesosies/", getAllProcesoSies);
router.get("/procesosiesoaactual/", getProcesoSiesOAactual);
router.get("/getprocesosiesoabyyear/:year", getProcesoSiesOAbyYear);
router.post("/procesosies/", postProcesoSies);
router.delete("/procesosies/:proceso_sies_id", deleteProcesoSies);

// Mantendor de comunas OTI routes
router.get("/comunas-OTI/", getAllComunas);
router.post("/comunas-OTI/", postComuna);
router.delete("/comunas-OTI/:comuna_id", deleteComuna);

// Matricula Unificada Pregrado
router.get("/mu_pregrado/", getAllMU_pregrado);
router.get("/mu_pregrado/:anio_proceso", getMU_pregrado);
router.get("/mu_pregrado/exportCSV/:anio_proceso/:etapa_actual", exportCSV);
router.post("/mu_pregrado/", postMU_pregrado);
router.delete("/mu_pregrado/:anio_proceso", deleteMU_pregrado);
router.delete(
  "/mu_pregrado/estudiantes/:anio_proceso/:etapa_actual",
  deleteEstudiantes
),
  router.patch("/mu_pregrado/:anio_proceso", confirm_MU_pregrado);
router.post(
  "/mu_pregrado/cargarCSV/:anio_proceso/:etapa_actual",
  postEstudiantesFromCSV
);

// Matricula Unificada Postgrado Postitulo
router.get("/mu_postgrado_postitulo/", getAllMU_postgrado);
router.get("/mu_postgrado_postitulo/:anio_proceso", getMU_postgrado);
router.get(
  "/mu_postgrado_postitulo/exportCSV/:anio_proceso/:etapa_actual",
  exportCSVPP
);
router.post("/mu_postgrado_postitulo/", postMU_postgrado);
router.delete("/mu_postgrado_postitulo/:anio_proceso", deleteMU_postgrado);
router.delete(
  "/mu_postgrado_postitulo/estudiantes/:anio_proceso/:etapa_actual",
  deleteEstudiantesPP
),
  router.patch("/mu_postgrado_postitulo/:anio_proceso", confirm_MU_PP);
router.post(
  "/mu_postgrado_postitulo/cargarCSV/:anio_proceso/:etapa_actual",
  postEstudiantesPpFromCSV
);

// Matricula unificada fechas pregrado
router.get("/mu_pregrado_fecha/", getAllFechas_pregrado);
router.get("/mu_pregrado_fecha/:anio_proceso", getFechas_pregrado);
router.post("/mu_pregrado_fecha/", postFechas_pregrado);

// Matricula unificada fechas postgrado-postitulo
router.get("/mu_postgrado_postitulo_fecha/", getAllFechas_postgrado);
router.get("/mu_postgrado_postitulo_fecha/:anio_proceso", getFechas_postgrado);
router.post("/mu_postgrado_postitulo_fecha/", postFechas_postgrado);

// Proceso routes (David)
router.get("/oa_proceso/", getAllOAProcesos);
router.get("/oa_proceso/:id", getOAProcesoById);
router.get("/oa_proceso/:proceso_id/subprocesos", getOASubProcesosByProcesoId);
router.post("/oa_proceso/", createOAProceso);
router.patch("/oa_proceso/:id", updateOAProceso);
router.patch("/oa_proceso/validate/:id", validateOAProceso);
router.delete("/oa_proceso/:id", deleteOAProceso);

// SubProceso routes (David)
router.get("/oa_subproceso/", getAllOASubProcesos);
router.get("/oa_subproceso/proceso/:proceso_id", getOASubProcesosByProcesoId);
router.get("/oa_subproceso/:id", getOASubProcesoById);
router.post("/oa_subproceso/", createOASubProceso);
router.post("/oa_subproceso/with_etapas/1", createSubProceso1WithEtapas);
router.post("/oa_subproceso/with_etapas/2", createSubProceso2WithEtapas);
router.patch("/oa_subproceso/:id", updateOASubProceso);
router.delete("/oa_subproceso/:id", deleteOASubProceso);
//cambiar a etapas
// OAEtapa routes
router.get("/oa_etapas", getAllOAEtapas);
router.get("/oa_etapas/subproceso/:subproceso_id", getOAEtapasBySubProcesoId);
router.get("/oa_etapa/:id", getOAEtapaById);
router.post("/oa_etapa", createOAEtapa);
router.put("/oa_etapa/:id", updateOAEtapa);
router.delete("/oa_etapa/:id", deleteOAEtapa);
// router.post("/oa_etapa/with_ofertas", createEtapaWithOfertasFromCSV);
router.post("/oa_etapa/process_csv", processCSVForOfertasAcademicas);
router.patch("/oa_etapa/validate/:id", validateOAEtapa);

// router.get("/cantCarrerasPerEtapa/:proceso_sies_id", getCantOAsPerEtapa);

// OAOfertaAcademica routes (David)
router.get("/oa_oferta_academica", getAllOAOfertaAcademicas);
router.get(
  "/oa_oferta_academica/etapa/:etapa_id",
  getOAOfertaAcademicasByEtapaId
);
router.get("/oa_oferta_academica/:id", getOAOfertaAcademicaById);
router.post("/oa_oferta_academica", createOAOfertaAcademica);
router.put("/oa_oferta_academica/:id", updateOAOfertaAcademica);
router.delete("/oa_oferta_academica/:id", deleteOAOfertaAcademica);
router.delete(
  "/oa_oferta_academica/etapa/:etapa_id",
  deleteOAOfertaAcademicasByEtapaId
);
router.get("/oa_cant_carreras_per_etapa", getOACantCarrerasPerEtapa);
router.post(
  "/oa_oferta_academica/import_csv/:etapa_id",
  importOfertasAcademicasFromCSV
);
router.post(
  "/oa_oferta_academica/import_csv_total",
  importOfertasAcademicasFromCSVTotal
);
router.delete(
  "/oa_oferta_academica/delete_csv_total",
  deleteOAOfertaAcademicasBySubprocesoEtapaTipo
);
router.get(
  "/oa_oferta_academica/etapa/:etapa_id/csv",
  getOAOfertaAcademicasByEtapaIdForCSV
);
router.get(
  "/oa_oferta_academica/etapa/totals/:etapaNumber/csv",
  getOAOfertaAcademicasTotalsByEtapaCSV
);

// PDF test
// router.post("/pdf/upload", savePdfAsBlob);
export default router;
