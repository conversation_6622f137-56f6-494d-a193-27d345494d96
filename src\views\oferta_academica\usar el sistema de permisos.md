usar el sistema de permisos


i need to add permissions to OA (oferta_academica) modules

i have these recursos:
"Administracion PIU"
"OA-postgrado"
"OA-pregrado"
"OA-postitulo"

for the roles, i need that the "Encargado" role can use the upload and delete oas for their respective recursos
for example in subproceso2 oa pregrado should be able to upload data to pregrado category but not to postgrado or postitulo

"usuario general" can only download the data


investigate the OTI module for more examples on how its done in the frontend and backend

validation can only be done by an administrator

the recurso "Administracion PIU" has the role "Administrador Sistema", and can do everything, only they can: validate,
upload totals, delete subprocesos, procesos and etapas, etc

back end
here is an example of the backend implementation in a function for validating a role in "bibliotecaController.js"
// Validate the user's roles
    const validRoles = [
      "Encargado",
      "Usuario General",
      "Administrador Sistema",
    ];

    const hasAccess = await checkPermisosUsuario(userEmail, validRoles, "OTI");
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }

    grey out the buttons if they dont have access
    remmeber to validate if they can do the actions in the backend too