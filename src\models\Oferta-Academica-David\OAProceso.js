import { Model, DataTypes } from "sequelize";
import sequelize from "../../database/database.js";


class OAProceso extends Model {}
OAProceso.init({
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
    },
    anio: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    fecha_inicial: {
        type: DataTypes.DATE,
        allowNull: true
    },
    fecha_final: {
        type: DataTypes.DATE,
        allowNull: true
    },
    subproceso_actual: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    creado_por: {
        type: DataTypes.STRING,
        allowNull: true,
        validate: { 
          isEmail: true, // Ensures the email format is valid
        },
      },
    validado: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false
    },
    version_lock: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 1
    }
}, {
    sequelize,
    modelName: 'OAProceso',
    tableName: 'oa_procesos',
    schema: 'oferta_academica',
    freezeTableName:true
});

export default OAProceso;