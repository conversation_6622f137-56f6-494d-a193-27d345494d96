import { Op } from "sequelize";
import OAProceso from "../../models/Oferta-Academic<PERSON>-<PERSON>/OAProceso.js";
import OASubProceso from "../../models/Oferta-<PERSON><PERSON>-<PERSON>/OASubProceso.js";
import { checkTokenLocal, getEmailFromToken } from "../../auth/auth.js";
import Usuario from "../../models/Usuario/usuario.js";
import { sequelize } from "../../database/database.js";
import { checkAtributosNecesarios } from "../../utils/checkAtributosNecesarios.js";

/**
 * Helper function to check database connection
 */
const checkDatabase = async () => {
  try {
    console.log("Checking database connection...");
    await sequelize.authenticate();
    console.log("Database is connected successfully.");
  } catch (error) {
    console.error("Database connection error:", error);
    throw new Error("Database not connected");
  }
};

/**
 * Get all OAProcesos
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getAllOAProcesos = async (req, res) => {
  try {
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Fetch all processes
    const procesos = await OAProceso.findAll({
      order: [["anio", "DESC"]]
    });

    res.status(200).json(procesos);
  } catch (error) {
    console.error("Error fetching all procesos:", error);
    res.status(500).json({ message: "Error fetching procesos" });
  }
};

/**
 * Get OAProceso by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getOAProcesoById = async (req, res) => {
  try {
    const { id } = req.params;
    const userToken = req.headers.authorization;
 
    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Find proceso by ID
    const proceso = await OAProceso.findByPk(id);

    if (!proceso) {
      return res.status(404).json({ message: "Proceso not found" });
    }

    res.status(200).json(proceso);
  } catch (error) {
    console.error("Error fetching proceso by ID:", error);
    res.status(500).json({ message: "Error fetching proceso" });
  }
};

/**
 * Create a new OAProceso
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const createOAProceso = async (req, res) => {
  try {
    const { anio, fecha_inicial, fecha_final } = req.body;
    const userToken = req.headers.authorization;

    // Required fields
    const requiredFields = ["anio"];

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredFields
    );

    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }

    // Get creator email from token
    const creador_email = await getEmailFromToken(userToken);

    // Find creator in database
    const creador = await Usuario.findOne({
      where: { email: { [Op.iLike]: creador_email } },
    });

    if (!creador) {
      return res.status(404).json({ message: "Usuario not found" });
    }

    // Create new proceso
    const newProceso = await OAProceso.create({
      anio,
      fecha_inicial,
      fecha_final,
      subproceso_actual:1,
      creado_por: creador.email,
    });

    res.status(201).json({
      message: "Proceso created successfully",
      proceso: newProceso,
    });
  } catch (error) {
    console.error("Error creating proceso:", error);
    res.status(500).json({ message: "Error creating proceso" });
  }
};

/**
 * Update an existing OAProceso
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const updateOAProceso = async (req, res) => {
  try {
    const { id } = req.params;
    const { anio, fecha_inicial, fecha_final } = req.body;
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Find proceso by ID
    const proceso = await OAProceso.findByPk(id);

    if (!proceso) {
      return res.status(404).json({ message: "Proceso not found" });
    }

    // Update proceso
    await proceso.update({
      anio: anio || proceso.anio,
      fecha_inicial: fecha_inicial || proceso.fecha_inicial,
      fecha_final: fecha_final || proceso.fecha_final,
      version_lock: proceso.version_lock + 1, // Increment version_lock
    });

    res.status(200).json({
      message: "Proceso updated successfully",
      proceso,
    });
  } catch (error) {
    console.error("Error updating proceso:", error);
    res.status(500).json({ message: "Error updating proceso" });
  }
};

/**
 * Delete an OAProceso
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const deleteOAProceso = async (req, res) => {
  try {
    const { id } = req.params;
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Find proceso by ID
    const proceso = await OAProceso.findByPk(id);

    if (!proceso) {
      return res.status(404).json({ message: "Proceso not found" });
    }

    // Delete proceso
    await proceso.destroy();

    res.status(200).json({ message: "Proceso deleted successfully" });
  } catch (error) {
    console.error("Error deleting proceso:", error);
    res.status(500).json({ message: "Error deleting proceso" });
  }
};

/**
 * Validate an OAProceso
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const validateOAProceso = async (req, res) => {
  try {
    const { id } = req.params;
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Find proceso by ID
    const proceso = await OAProceso.findByPk(id);

    if (!proceso) {
      return res.status(404).json({ message: "Proceso not found" });
    }

    // Check if all subprocesos are validated
    const subprocesos = await OASubProceso.findAll({
      where: { proceso_id: id }
    });

    if (subprocesos.length === 0) {
      return res.status(400).json({
        message: "No subprocesos found for this proceso"
      });
    }

    const allSubprocesosValidated = subprocesos.every(subproceso => subproceso.validado === true);

    if (!allSubprocesosValidated) {
      return res.status(400).json({
        message: "All subprocesos must be validated before validating the proceso"
      });
    }

    // Update the proceso as validated
    await proceso.update({ validado: true });

    res.status(200).json({
      message: "Proceso validated successfully",
      proceso: proceso,
      allSubprocesosValidated: true
    });
  } catch (error) {
    console.error("Error validating proceso:", error);
    res.status(500).json({ message: "Error validating proceso" });
  }
};
