// Test script for OA Permission System
// This script can be used to test the permission logic

// Mock permission data for testing
const testPermissions = {
  adminUser: [
    { rol: 'Administrador Sistema', recurso: 'Administracion PIU' }
  ],
  encargadoPregrado: [
    { rol: 'Encargado', recurso: 'OA-pregrado' }
  ],
  encargadoPostgrado: [
    { rol: 'Encargado', recurso: 'OA-postgrado' }
  ],
  encargadoPostitulo: [
    { rol: 'Encargado', recurso: 'OA-postitulo' }
  ],
  usuarioGeneralPregrado: [
    { rol: 'Usuario General', recurso: 'OA-pregrado' }
  ],
  usuarioGeneralPostgrado: [
    { rol: 'Usuario General', recurso: 'OA-postgrado' }
  ],
  usuarioGeneralPostitulo: [
    { rol: 'Usuario General', recurso: 'OA-postitulo' }
  ]
};

// Permission checking function (same as implemented in frontend)
const hasOAAccess = (permissionsList, allowedRol, categoria = null) => {
  // Administrador Sistema with Administracion PIU can do everything
  if (permissionsList.some(user => user.rol === 'Administrador Sistema' && user.recurso === 'Administracion PIU')) {
    return true;
  }

  // Map categories to their respective recursos
  const categoriaToRecurso = {
    'pregrado': 'OA-pregrado',
    'postgrado': 'OA-postgrado', 
    'postitulo': 'OA-postitulo'
  };

  // If no category specified, check if user has access to any OA resource
  if (!categoria) {
    return permissionsList.some(user => 
      user.rol === allowedRol && 
      (user.recurso === 'OA-pregrado' || user.recurso === 'OA-postgrado' || user.recurso === 'OA-postitulo')
    );
  }

  // Check specific category access
  const requiredRecurso = categoriaToRecurso[categoria.toLowerCase()];
  if (!requiredRecurso) {
    return false;
  }

  return permissionsList.some(user => user.rol === allowedRol && user.recurso === requiredRecurso);
};

// Test cases
const runTests = () => {
  console.log('=== OA Permission System Tests ===\n');

  // Test 1: Administrador Sistema
  console.log('Test 1: Administrador Sistema');
  const admin = testPermissions.adminUser;
  console.log('- Can validate:', hasOAAccess(admin, 'Administrador Sistema')); // Should be true
  console.log('- Can upload pregrado:', hasOAAccess(admin, 'Administrador Sistema', 'pregrado')); // Should be true
  console.log('- Can upload postgrado:', hasOAAccess(admin, 'Administrador Sistema', 'postgrado')); // Should be true
  console.log('- Can upload postitulo:', hasOAAccess(admin, 'Administrador Sistema', 'postitulo')); // Should be true
  console.log('');

  // Test 2: Encargado Pregrado
  console.log('Test 2: Encargado Pregrado');
  const encargadoPregrado = testPermissions.encargadoPregrado;
  console.log('- Can validate:', hasOAAccess(encargadoPregrado, 'Administrador Sistema')); // Should be false
  console.log('- Can upload pregrado:', hasOAAccess(encargadoPregrado, 'Encargado', 'pregrado')); // Should be true
  console.log('- Can upload postgrado:', hasOAAccess(encargadoPregrado, 'Encargado', 'postgrado')); // Should be false
  console.log('- Can upload postitulo:', hasOAAccess(encargadoPregrado, 'Encargado', 'postitulo')); // Should be false
  console.log('- Can export pregrado:', hasOAAccess(encargadoPregrado, 'Usuario General', 'pregrado')); // Should be false (wrong role)
  console.log('');

  // Test 3: Usuario General Postgrado
  console.log('Test 3: Usuario General Postgrado');
  const usuarioPostgrado = testPermissions.usuarioGeneralPostgrado;
  console.log('- Can validate:', hasOAAccess(usuarioPostgrado, 'Administrador Sistema')); // Should be false
  console.log('- Can upload postgrado:', hasOAAccess(usuarioPostgrado, 'Encargado', 'postgrado')); // Should be false
  console.log('- Can export postgrado:', hasOAAccess(usuarioPostgrado, 'Usuario General', 'postgrado')); // Should be true
  console.log('- Can export pregrado:', hasOAAccess(usuarioPostgrado, 'Usuario General', 'pregrado')); // Should be false
  console.log('');

  // Test 4: Cross-category access prevention
  console.log('Test 4: Cross-category Access Prevention');
  const encargadoPostitulo = testPermissions.encargadoPostitulo;
  console.log('- Postitulo user can upload postitulo:', hasOAAccess(encargadoPostitulo, 'Encargado', 'postitulo')); // Should be true
  console.log('- Postitulo user can upload pregrado:', hasOAAccess(encargadoPostitulo, 'Encargado', 'pregrado')); // Should be false
  console.log('- Postitulo user can upload postgrado:', hasOAAccess(encargadoPostitulo, 'Encargado', 'postgrado')); // Should be false
  console.log('');

  // Test 5: Button visibility logic
  console.log('Test 5: Button Visibility Logic');
  console.log('=== Upload Button (Encargado + Admin) ===');
  Object.entries(testPermissions).forEach(([userType, permissions]) => {
    const canUploadPregrado = hasOAAccess(permissions, 'Encargado', 'pregrado') || hasOAAccess(permissions, 'Administrador Sistema');
    console.log(`${userType} can see upload button for pregrado:`, canUploadPregrado);
  });
  
  console.log('\n=== Export Button (All roles) ===');
  Object.entries(testPermissions).forEach(([userType, permissions]) => {
    const canExportPostgrado = hasOAAccess(permissions, 'Usuario General', 'postgrado') || 
                               hasOAAccess(permissions, 'Encargado', 'postgrado') || 
                               hasOAAccess(permissions, 'Administrador Sistema');
    console.log(`${userType} can see export button for postgrado:`, canExportPostgrado);
  });

  console.log('\n=== Validation Button (Admin only) ===');
  Object.entries(testPermissions).forEach(([userType, permissions]) => {
    const canValidate = hasOAAccess(permissions, 'Administrador Sistema');
    console.log(`${userType} can see validation button:`, canValidate);
  });
};

// Run the tests
if (typeof module !== 'undefined' && module.exports) {
  // Node.js environment
  module.exports = { hasOAAccess, testPermissions, runTests };
} else {
  // Browser environment
  runTests();
}

// Usage instructions:
// 1. In browser console: Copy and paste this entire script, then call runTests()
// 2. In Node.js: const { runTests } = require('./test_permissions.js'); runTests();
