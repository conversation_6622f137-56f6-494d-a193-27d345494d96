# OA Permission System Implementation Guide

## Overview
This document outlines the comprehensive permission system implemented for the Oferta Académica (OA) modules.

## Permission Structure

### Resources
- `Administracion PIU` - Full system administration
- `OA-pregrado` - Pregrado academic offerings
- `OA-postgrado` - Postgrado academic offerings  
- `OA-postitulo` - Postitulo academic offerings

### Roles and Permissions

#### Administrador Sistema (with Administracion PIU resource)
- **Can do everything**: validate, upload totals, delete subprocesos, procesos and etapas
- **Access**: All categories (pregrado, postgrado, postitulo)
- **Actions**: Create, Read, Update, Delete, Validate, Import/Export

#### Encargado (with specific OA resource)
- **Can**: Upload and delete OAs for their respective recursos
- **Access**: Only their assigned category (e.g., OA-pregrado users can only access pregrado data)
- **Actions**: Create, Read, Update, Delete, Import/Export (category-specific)
- **Cannot**: Validate (only Administrador Sistema can validate)

#### Usuario General (with specific OA resource)
- **Can**: Only download/export data
- **Access**: Only their assigned category
- **Actions**: Read, Export (category-specific)
- **Cannot**: Create, Update, Delete, Validate, Import

## Implementation Details

### Frontend Changes

#### 1. Enhanced hasOAAccess Function
```javascript
const hasOAAccess = (allowedRol, categoria = null) => {
    // Administrador Sistema with Administracion PIU can do everything
    if (permissionsList.value.some(user => user.rol === 'Administrador Sistema' && user.recurso === 'Administracion PIU')) {
        return true;
    }

    // Map categories to their respective recursos
    const categoriaToRecurso = {
        'pregrado': 'OA-pregrado',
        'postgrado': 'OA-postgrado', 
        'postitulo': 'OA-postitulo'
    };

    // Category-specific access check
    const requiredRecurso = categoriaToRecurso[categoria?.toLowerCase()];
    return permissionsList.value.some(user => user.rol === allowedRol && user.recurso === requiredRecurso);
};
```

#### 2. Updated Button Permissions
- **Validation buttons**: Only `hasOAAccess('Administrador Sistema')`
- **Upload/Delete buttons**: `hasOAAccess('Encargado', categoria) || hasOAAccess('Administrador Sistema')`
- **Export buttons**: `hasOAAccess('Usuario General', categoria) || hasOAAccess('Encargado', categoria) || hasOAAccess('Administrador Sistema')`

### Backend Changes

#### 1. Permission Validation Functions
```javascript
const validateOAPermissions = async (userEmail, validRoles, categoria = null) => {
    // Check admin access first
    const adminAccess = await checkPermisosUsuario(userEmail, ['Administrador Sistema'], 'Administracion PIU');
    if (adminAccess) return true;

    // Check category-specific access
    const resource = getResourceFromCategoria(categoria);
    return await checkPermisosUsuario(userEmail, validRoles, resource);
};
```

#### 2. Controller Updates
- **oaOfertaAcademicaController.js**: Added permission checks to create, update, delete, and import methods
- **oaProcesoController.js**: Added permission checks to create, delete, and validate methods
- All methods now validate user permissions before executing operations

## Testing Scenarios

### Test Case 1: Administrador Sistema
- **Setup**: User with role "Administrador Sistema" and resource "Administracion PIU"
- **Expected**: Can see and use all buttons (validate, upload, delete, export) for all categories
- **Test**: Try uploading pregrado data, postgrado data, and postitulo data - all should work

### Test Case 2: Encargado Pregrado
- **Setup**: User with role "Encargado" and resource "OA-pregrado"
- **Expected**: Can upload/delete pregrado data, cannot validate, cannot access postgrado/postitulo
- **Test**: 
  - Upload pregrado data ✓
  - Try to upload postgrado data ✗ (should be blocked)
  - Validation button should not appear ✗

### Test Case 3: Usuario General Postgrado
- **Setup**: User with role "Usuario General" and resource "OA-postgrado"
- **Expected**: Can only export postgrado data, no create/update/delete/validate permissions
- **Test**:
  - Export postgrado data ✓
  - Upload buttons should not appear ✗
  - Delete buttons should not appear ✗

### Test Case 4: Cross-Category Access Prevention
- **Setup**: User with role "Encargado" and resource "OA-postitulo"
- **Expected**: Cannot access pregrado or postgrado data
- **Test**:
  - Try to upload pregrado data via API ✗ (should return 403)
  - Try to delete postgrado data via API ✗ (should return 403)
  - Can only see postitulo-related buttons ✓

## Files Modified

### Frontend
- `src/views/oferta_academica/pp_oa_etapas.vue`
- `src/views/oferta_academica/pp_oa_procesos.vue`
- `src/views/oferta_academica/pp_oa_subprocesos.vue`

### Backend
- `src/controllers/Oferta-Academica-David/oaOfertaAcademicaController.js`
- `src/controllers/Oferta-Academica-David/oaProcesoController.js`

## Security Features
1. **Frontend button visibility** controlled by permissions
2. **Backend API validation** prevents unauthorized access
3. **Category-based isolation** ensures users can only access their assigned data
4. **Role-based action restrictions** (e.g., only admins can validate)
